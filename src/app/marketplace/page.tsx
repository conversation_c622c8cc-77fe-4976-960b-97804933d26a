"use client";

import MarketplaceHeader from "@/app/marketplace/marketplace-header";
import MarketplaceFooter from "@/app/marketplace/marketplace-footer";
import { GiftList } from "@/components/ui/GiftList";
import { useState } from "react";

export default function MarketplacePage() {
  const [loading, setLoading] = useState(true);
  const [checkLoading, setCheckLoading] = useState(false);
  const [hasNext, setHasNext] = useState(true);
  const [loadedCount, setLoadedCount] = useState(0);

  return (
    <div className="min-h-screen bg-slate-900 flex flex-col">
      <MarketplaceHeader />

      <main className="flex-1">
        <div className="max-w-6xl mx-auto p-6">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-white mb-2">My Gifts</h2>
            <p className="text-gray-400">Your received gifts</p>
          </div>

          <GiftList
            isPending={false}
            telegramId={0}
            length={loadedCount}
            infiniteScrollLoader={{
              isLoading: loading,
              setIsLoading: setLoading,
              setCheckLoading: setCheckLoading,
              setHasNext: setHasNext,
              setLoadedCount: setLoadedCount,
            }}
          />
        </div>
      </main>

      <MarketplaceFooter />
    </div>
  );
}
