{"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "unused-imports"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "rules": {"no-unused-vars": "off", "unused-imports/no-unused-imports": "error", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}], "@typescript-eslint/no-unused-vars": ["error", {"varsIgnorePattern": "^_", "args": "after-used"}]}, "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}}