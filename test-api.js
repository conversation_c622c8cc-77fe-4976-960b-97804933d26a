// Simple test to verify the external gift API is accessible
const BASE_URL = "https://giftapi.noaccess.xyz";

async function testGiftAPI() {
  try {
    console.log("Testing connection to gift API...");
    
    // Test basic connectivity
    const response = await fetch(`${BASE_URL}/gift/list`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        // Note: In real usage, this would need proper Telegram WebApp initData
        "Authorization": "TelegramWebApp test"
      }
    });
    
    console.log("Response status:", response.status);
    console.log("Response headers:", Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log("API Response:", data);
    } else {
      const errorText = await response.text();
      console.log("Error response:", errorText);
    }
    
  } catch (error) {
    console.error("Network error:", error);
  }
}

testGiftAPI();
