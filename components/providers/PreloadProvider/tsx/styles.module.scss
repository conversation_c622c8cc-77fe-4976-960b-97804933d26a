.loadingScreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--theme-main-bg, --theme-bg, --tg-theme-bg-color);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loadingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.giftBox {
  position: relative;
  width: 80px;
  height: 80px;
  animation: bounce 1.5s ease-in-out infinite;
}

.giftLid {
  position: absolute;
  top: -20px;
  left: 0;
  width: 100%;
  height: 25px;
  background: var(--theme-button, --tg-theme-button-color);
  border-radius: 4px;
  transform-origin: bottom;
  animation: lidShake 1.5s ease-in-out infinite;
}

.giftBow {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 15px;
  border-radius: 15px;
  background: var(--theme-button, --tg-theme-button-color);
  filter: brightness(120%);

  &::before,
  &::after {
    content: "";
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: inherit;
    top: -5px;
  }

  &::before {
    left: -5px;
  }
  &::after {
    right: -5px;
  }
}

.giftBoxBody {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 70px;
  background: var(--theme-button, --tg-theme-button-color);
  border-radius: 4px;

  &::before,
  &::after {
    content: "";
    position: absolute;
    top: 0;
    width: 4px;
    height: 100%;
    background: var(--theme-main-bg, --theme-bg, --tg-theme-bg-color);
  }

  &::before {
    left: 50%;
    transform: translateX(-50%);
  }
  &::after {
    top: 50%;
    left: 0;
    width: 100%;
    height: 4px;
    transform: translateY(-50%);
  }
}

.progressWrapper {
  position: relative;
  width: 200px;
  height: 4px;
  background: var(
    --theme-tiles-bg,
    --theme-secondary-bg,
    --tg-theme-secondary-bg-color
  );
  border-radius: 4px;
  overflow: hidden;
}

.progressBar {
  height: 100%;
  background: var(--theme-button, --tg-theme-button-color);
  border-radius: 4px;
  transition: width 0.3s ease-out;
}

.progressText {
  position: absolute;
  top: 12px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 14px;
  color: var(--theme-text, --tg-theme-text-color);
  font-weight: 500;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes lidShake {
  0%,
  100% {
    transform: rotate(0);
  }
  25% {
    transform: rotate(-5deg);
  }
  75% {
    transform: rotate(5deg);
  }
}
