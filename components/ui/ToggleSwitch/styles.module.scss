.switchBackground {
  height: 2.1rem;
  width: 5.1875rem;
  background-color: var(--theme-section-separator-bg);
  border: 0.125rem solid var(--tg-theme-border-color);
  border-radius: 2rem;
  padding: 0.1875rem;
  cursor: pointer;
  border: 1px solid var(--tg-theme-border-color);
  .switchInner {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-evenly;

    .switchButton {
      position: absolute;
      background-color: var(--theme-main-bg);
      box-shadow: 0 0 0.125rem 0.025rem #00000041;
      height: 100%;
      left: 0;
      width: 2.4375rem;
      height: 1.75rem;
      border-radius: 1.5rem;
      z-index: 1;
      transition: left 0.3s ease, right 0.3s ease;
    }
    .switchText {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--font-small);
      font-weight: 500;
      z-index: 2;
      transition: color 0.2s ease;
      width: fit-content;

      svg {
        width: 1.25rem;
        height: 1.25rem;
      }
      &:first-child {
        padding-left: 0.2rem;
      }
      &:last-child {
        padding-right: 0.2rem;
      }
    }
  }
}
