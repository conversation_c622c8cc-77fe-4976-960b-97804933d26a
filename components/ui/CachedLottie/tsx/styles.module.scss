.loadingContainer {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  background-color: var(--theme-tiles-bg);
  height: 100%;
}

.loadingPulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  animation: pulse 1.5s ease-in-out infinite;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(var(--theme-text-rgb), 0.1) 50%,
    transparent 100%
  );
}

@keyframes pulse {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
