.giftsTable {
  width: 100%;
  border-radius: 0.75rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .noGifts {
    background-color: var(--theme-tiles-bg);
    text-align: center;
    padding: 2rem 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    font-size: var(--font-medium);
    width: 100%;
    .noGiftsIcon {
      width: 30vw;
      height: 30vw;
      max-height: 30vh;
      max-width: 30vh;
    }
    a {
      color: var(--theme-link);
    }
  }
  &:not(.noGifts) {
    .giftsGrid {
      display: grid;
      grid-template-columns: repeat(3, minmax(0, 1fr));
      grid-auto-rows: 1fr;
      gap: 0.5rem;
      width: 100%;
      .giftCard {
        overflow: hidden;
        background-color: var(--theme-tiles-bg);
        border: 1px solid var(--tg-theme-border-color);
        border-radius: 0.75rem;
        // min-width: calc(33vw - 1.5rem);
        // // last three cards in the grid
        // &:nth-last-child(-n + 3) {
        //   margin-bottom: 0.5rem;
        // }
        .loadingCard {
          width: calc(33vw - 1rem);
          min-width: calc(33vw - 1rem);
          min-height: calc((min(33vw - 3rem, 30vh)) + 4.25rem);
          position: relative;
          background-color: var(--theme-tiles-bg);
          opacity: 0.5;
          overflow: hidden;
          border: 1px solid transparent;
          // height: 10.25rem;
          border-radius: 0.75rem;
          animation: borderOpacity 2s infinite linear;
          @keyframes borderOpacity {
            20% {
              border-color: transparent;
            }
            40% {
              border-color: var(--theme-subtitle);
            }
            60% {
              border-color: transparent;
            }
          }

          &::after {
            content: "";
            position: absolute;
            top: 0;
            left: -200%;
            width: 200%;
            height: 100%;
            background: linear-gradient(
              90deg,
              transparent 0%,
              rgba(var(--theme-subtitle-rgb), 0.2) 20%,
              rgba(var(--theme-subtitle-rgb), 0.4) 45%,
              rgba(var(--theme-subtitle-rgb), 0.2) 80%,
              transparent 100%
            );
            animation: shimmer 2s infinite ease-in-out;
          }
        }
      }
    }
  }
}

@keyframes shimmer {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(200%);
  }
}

// .loadingCard {
//   width: 100%;
//   height: calc((min(33vw - 3rem, 30vh)) + 4.25rem);
//   background-color: var(--theme-tiles-bg);
//   border-radius: 0.75rem;
//   overflow: hidden;
//   position: relative;

//   &::after {
//     content: "";
//     position: absolute;
//     top: 0;
//     left: -200%;
//     width: 200%;
//     height: 100%;
//     background: linear-gradient(
//       90deg,
//       transparent 0%,
//       rgba(var(--theme-subtitle-rgb), 0.2) 20%,
//       rgba(var(--theme-subtitle-rgb), 0.4) 45%,
//       rgba(var(--theme-subtitle-rgb), 0.2) 80%,
//       transparent 100%
//     );
//     animation: shimmer 2s infinite ease-in-out;
//   }
// }

// @keyframes shimmer {
//   from {
//     transform: translateX(0);
//   }
//   to {
//     transform: translateX(200%);
//   }
// }
