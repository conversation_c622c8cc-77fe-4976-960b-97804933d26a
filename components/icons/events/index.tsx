import { FC } from "react";
interface IconProps {
  width?: string;
  height?: string;
}

export const Sent: FC<IconProps> = ({ width = "20px", height = "20px" }) => {
  return (
    <svg width={width} height={height} viewBox="0 0 20 20" fill="none">
      <rect x="0" y="0" width="20" height="20" rx="10" fill="#AF51DE" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.6995 9.30025C10.9338 9.53456 10.9338 9.91446 10.6995 10.1488L8.93491 11.9134C8.70059 12.1477 8.3207 12.1477 8.08638 11.9134C7.85207 11.6791 7.85207 11.2992 8.08638 11.0648L9.85098 9.30025C10.0853 9.06593 10.4652 9.06593 10.6995 9.30025Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.0245 6.90377C13.0245 6.90377 13.0245 6.90377 13.0245 6.90377L5.93848 9.26596C5.91959 9.27226 5.91391 9.2792 5.91128 9.28268C5.90682 9.28858 5.90167 9.2993 5.90029 9.31412C5.89892 9.32895 5.90201 9.34046 5.90533 9.34713C5.9073 9.35108 5.91162 9.35895 5.92901 9.36861L8.80192 10.9647C8.89986 11.0191 8.98061 11.0998 9.03502 11.1978L10.631 14.0706C10.6407 14.088 10.6486 14.0924 10.6526 14.0944C10.6592 14.0977 10.6707 14.1008 10.6856 14.0994C10.7004 14.098 10.7111 14.0929 10.717 14.0884C10.7205 14.0858 10.7274 14.0801 10.7337 14.0612L13.0959 6.97522C13.1015 6.95847 13.1 6.94978 13.0987 6.94473C13.0968 6.93732 13.0918 6.92718 13.0822 6.91752C13.0725 6.90786 13.0624 6.90289 13.055 6.90098C13.0499 6.89967 13.0412 6.89819 13.0245 6.90377ZM12.645 5.76535C13.627 5.438 14.5617 6.37264 14.2343 7.3547L11.8721 14.4407C11.5264 15.478 10.1126 15.6087 9.58204 14.6533C9.58202 14.6533 9.58206 14.6534 9.58204 14.6533L8.06929 11.9304L5.34645 10.4177C5.34642 10.4177 5.34649 10.4177 5.34645 10.4177C4.39112 9.88712 4.52167 8.47334 5.55898 8.12755C5.55897 8.12756 5.55898 8.12755 5.55898 8.12755L12.645 5.76535Z"
        fill="white"
      />
    </svg>
  );
};

export const Purchased: FC<IconProps> = ({
  width = "20px",
  height = "20px",
}) => {
  return (
    <svg width={width} height={height} viewBox="0 0 20 20" fill="none">
      <rect x="0" y="0" width="20" height="20" rx="10" fill="#007AFF" />
      <path
        d="M14.9445 8.19567L14.0992 6.22461C13.9738 5.93237 13.9112 5.78623 13.8102 5.67897C13.721 5.58416 13.6108 5.51156 13.4885 5.46696C13.3501 5.4165 13.191 5.4165 12.8728 5.4165H7.12726C6.80908 5.4165 6.65 5.4165 6.51157 5.46696C6.38923 5.51156 6.27905 5.58416 6.18984 5.67897C6.0889 5.78623 6.02623 5.93236 5.9009 6.22461L5.05558 8.19567H14.9445Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.83051 10.6334C6.05798 10.7168 6.30374 10.7623 6.56017 10.7623C6.98257 10.7623 7.37641 10.6384 7.70679 10.4256C8.03717 10.6384 8.43101 10.7623 8.85341 10.7623C9.2758 10.7623 9.66964 10.6384 10 10.4256C10.3304 10.6384 10.7242 10.7623 11.1466 10.7623C11.569 10.7623 11.9629 10.6384 12.2933 10.4256C12.6236 10.6384 13.0175 10.7623 13.4399 10.7623C13.6963 10.7623 13.9421 10.7168 14.1695 10.6334V13.2498C14.1695 13.7165 14.1695 13.9499 14.0787 14.1282C13.9987 14.285 13.8711 14.4124 13.7142 14.4923C13.5358 14.5832 13.3023 14.5832 12.8353 14.5832H7.16476C6.69773 14.5832 6.46421 14.5832 6.28583 14.4923C6.12892 14.4124 6.00135 14.285 5.9214 14.1282C5.83051 13.9499 5.83051 13.7165 5.83051 13.2498V10.6334ZM6.6644 12.3332C6.6644 12.0998 6.6644 11.9831 6.70984 11.894C6.74982 11.8156 6.8136 11.7519 6.89206 11.7119C6.98125 11.6665 7.098 11.6665 7.33152 11.6665H8.91594C9.14945 11.6665 9.26621 11.6665 9.3554 11.7119C9.43385 11.7519 9.49764 11.8156 9.53761 11.894C9.58306 11.9831 9.58306 12.0998 9.58306 12.3332V13.0832C9.58306 13.3165 9.58306 13.4332 9.53761 13.5223C9.49764 13.6007 9.43385 13.6645 9.3554 13.7044C9.26621 13.7498 9.14945 13.7498 8.91594 13.7498H7.33152C7.098 13.7498 6.98125 13.7498 6.89206 13.7044C6.8136 13.6645 6.74982 13.6007 6.70984 13.5223C6.6644 13.4332 6.6644 13.3165 6.6644 13.0832V12.3332Z"
        fill="white"
      />
      <path
        d="M6.56015 10.2082C5.73164 10.2082 5.05364 9.56422 5 8.74984H15C14.9464 9.56422 14.2684 10.2082 13.4399 10.2082C12.9868 10.2082 12.5788 10.0156 12.2932 9.70798C12.0077 10.0156 11.5996 10.2082 11.1466 10.2082C10.6936 10.2082 10.2856 10.0156 10 9.70798C9.71444 10.0156 9.30641 10.2082 8.85338 10.2082C8.40035 10.2082 7.99232 10.0156 7.70677 9.70798C7.42121 10.0156 7.01318 10.2082 6.56015 10.2082Z"
        fill="white"
      />
    </svg>
  );
};

export const Received: FC<IconProps> = ({
  width = "20px",
  height = "20px",
}) => {
  return (
    <svg width={width} height={height} viewBox="0 0 20 20" fill="none">
      <rect x="0" y="0" width="20" height="20" rx="10" fill="#35C759" />
      <path
        d="M13.9 6.3999H6.10005C5.93436 6.3999 5.80005 6.53422 5.80005 6.6999V7.2999C5.80005 7.46559 5.93436 7.5999 6.10005 7.5999H13.9C14.0657 7.5999 14.2 7.46559 14.2 7.2999V6.6999C14.2 6.53422 14.0657 6.3999 13.9 6.3999Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.19995 6.6998C5.19995 6.20275 5.60289 5.7998 6.09995 5.7998H13.9C14.397 5.7998 14.8 6.20275 14.8 6.6998V7.2998C14.8 7.79686 14.397 8.1998 13.9 8.1998H6.09995C5.60289 8.1998 5.19995 7.79686 5.19995 7.2998V6.6998Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.9999 10C13.3313 10 13.5999 10.2686 13.5999 10.6V14.2C13.5999 14.5314 13.3313 14.8 12.9999 14.8C12.6685 14.8 12.3999 14.5314 12.3999 14.2V10.6C12.3999 10.2686 12.6685 10 12.9999 10Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.0756 12.2753C11.31 12.041 11.6899 12.041 11.9242 12.2753L12.9999 13.3511L14.0756 12.2753C14.31 12.041 14.6899 12.041 14.9242 12.2753C15.1585 12.5097 15.1585 12.8896 14.9242 13.1239L13.4242 14.6239C13.1899 14.8582 12.81 14.8582 12.5756 14.6239L11.0756 13.1239C10.8413 12.8896 10.8413 12.5097 11.0756 12.2753Z"
        fill="white"
      />
      <path
        d="M10.2274 13.9725C9.88785 13.6329 9.70005 13.1805 9.70005 12.6999C9.70005 12.2193 9.88725 11.7669 10.2274 11.4273C10.4956 11.1591 10.8334 10.9857 11.2 10.9245V10.5999C11.2 10.1373 11.3806 9.7191 11.668 9.3999H5.80005V11.7999C5.80005 13.1235 6.87645 14.1999 8.20005 14.1999H10.4548L10.2274 13.9725Z"
        fill="white"
      />
    </svg>
  );
};
