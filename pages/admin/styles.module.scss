.container {
  margin: 0 auto;
  height: calc(var(--tg-viewport-height) - 4rem);
  overflow-y: auto;
  overflow-x: hidden;
  transform-origin: center bottom;
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--theme-bg);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--theme-button);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--theme-button);
    filter: brightness(0.8);
  }

  scrollbar-width: thin;
  scrollbar-color: var(--theme-button) var(--theme-bg);
}

.content {
  display: flex;
  height: fit-content;
  flex-direction: column;
  padding: 2rem 1rem;
}

.tabs {
  display: flex;
  flex-direction: row;
  margin: 0 auto 1rem;
  background-color: var(--theme-secondary-bg);
  border: 1px solid var(--theme-separator);
  border-radius: 0.5rem;
  .tab {
    font-size: var(--font-very-small);
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    color: var(--tg-theme-secondary-text-color);
    &.active {
      background-color: var(--theme-button);
      color: var(--theme-button-text);
    }
  }
}

.tabContent {
  display: flex;
  flex-direction: column;
  padding: 1rem;

  .tabContentTitle {
    font-size: var(--font-large);
    text-align: center;
    font-weight: bold;
    margin-bottom: 1rem;
  }

  .adminCard {
    display: flex;
    flex-direction: row;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: var(--theme-secondary-bg);
    border: 1px solid var(--theme-separator);
    border-radius: 0.5rem;
    justify-content: space-between;
    align-items: center;
    &.superAdmin {
      background-color: var(--theme-button);
      color: var(--theme-button-text);
    }

    .adminCardInfo {
      display: flex;
      flex-direction: column;
      margin-right: 1rem;
      .adminCardName {
        font-size: var(--font-medium);
        font-weight: bold;
        margin-bottom: 0.5rem;
      }
      .adminCardTelegramId {
        font-size: var(--font-small);
        margin-bottom: 0.5rem;
      }
    }
    .adminCardButton {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      button {
        font-size: var(--font-small);
        padding: 0.25rem 0.5rem;
        border-radius: 0.5rem;
        cursor: pointer;
        color: var(--tg-theme-secondary-text-color);
        background-color: var(--theme-button);
        border: 1px solid var(--theme-separator);
        &:hover {
          background-color: var(--theme-button);
          filter: brightness(0.8);
        }
      }
    }
  }
  .giftsGrid {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    grid-auto-rows: 1fr;
    gap: 0.75rem;
    margin-top: 2rem;

    .giftCard {
      overflow: hidden;
    }
  }
}
