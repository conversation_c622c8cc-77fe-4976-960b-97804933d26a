.container {
  margin: 0 auto;
  height: calc(var(--tg-viewport-height) + 2.5rem);
  overflow-y: auto;
  overflow-x: hidden;
  transform-origin: center bottom;
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--theme-main-bg);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--theme-button);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--theme-button);
    filter: brightness(0.8);
  }

  scrollbar-width: thin;
  scrollbar-color: var(--theme-button) var(--theme-main-bg);
}

.content {
  display: flex;
  height: fit-content;
  flex-direction: column;
  padding: 2rem 1rem;
  margin-bottom: 5.5rem;
  width: 100%;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: var(--theme-accent);
  gap: 0.5rem;
  text-align: center;

  h1 {
    font-size: var(--font-large);
    margin-top: 1rem;
    color: var(--theme-text);
  }

  p {
    font-size: var(--font-medium);
    color: var(--theme-subtitle);
  }
}

:global(.tdesktop) .header {
  transition: transform 0.1s;
}

.giftsGrid {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  grid-auto-rows: 1fr;
  gap: 0.75rem;
  margin-top: 2rem;
  .giftCard {
    overflow: hidden;
  }
}

.loadingCard {
  height: calc((min(50vw - 3rem, 45vh)) + 6.875rem);
  background-color: var(--theme-tiles-bg);
  border-radius: 0.75rem;
  overflow: hidden;
  position: relative;
  border: 1px solid transparent;
  animation: borderOpacity 2s infinite linear;
  @keyframes borderOpacity {
    20% {
      border-color: transparent;
    }
    40% {
      border-color: var(--theme-subtitle);
    }
    60% {
      border-color: transparent;
    }
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: -200%;
    width: 200%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(var(--theme-subtitle-rgb), 0.2) 20%,
      rgba(var(--theme-subtitle-rgb), 0.4) 45%,
      rgba(var(--theme-subtitle-rgb), 0.2) 80%,
      transparent 100%
    );
    animation: shimmer 2s infinite ease-in-out;
  }
}

@keyframes shimmer {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(200%);
  }
}
