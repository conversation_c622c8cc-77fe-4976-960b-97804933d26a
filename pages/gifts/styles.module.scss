.container {
  margin: 0 auto;
  height: calc(var(--tg-viewport-height) + 2.5rem);
  overflow-y: auto;
  overflow-x: hidden;
  transform-origin: center bottom;
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--theme-header-bg);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--theme-button);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--theme-button);
    filter: brightness(0.8);
  }

  scrollbar-width: thin;
  scrollbar-color: var(--theme-button) var(--theme-main-bg);
}

.content {
  display: flex;
  height: fit-content;
  flex-direction: column;
  padding: 1.5rem 1rem;
  margin-bottom: 5.5rem;
  gap: 1.5rem;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 3rem;
  color: var(--theme-accent);
  gap: 0.5rem;

  h1 {
    font-size: var(--font-large);
    margin-top: 1rem;
    color: var(--theme-text);
  }

  p {
    font-size: var(--font-medium);
    color: var(--theme-subtitle);
    max-width: 20rem;
  }
}

:global(.tdesktop) .header {
  transition: transform 0.1s;
}
.giftsTable {
  width: 100%;
  border-radius: 0.75rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .noGifts {
    background-color: var(--theme-tiles-bg);
    text-align: center;
    padding: 2rem 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    font-size: var(--font-medium);
    width: 100%;
    .noGiftsIcon {
      width: 30vw;
      height: 30vw;
      max-height: 30vh;
      max-width: 30vh;
    }
    a {
      color: var(--theme-link);
    }
  }
  &:not(.noGifts) {
    .giftsGrid {
      display: grid;
      grid-template-columns: repeat(3, minmax(0, 1fr));
      grid-auto-rows: 1fr;
      gap: 0.5rem;
      width: 100%;
      .giftCard {
        overflow: hidden;
      }
    }
  }
}
