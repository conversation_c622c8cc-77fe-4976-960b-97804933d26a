{"name": "gifter", "version": "1.0.0", "main": "index.js", "scripts": {"clean": "rm -rf ./dist/react", "build": "webpack --mode production", "start": "cross-env NODE_ENV=production webpack serve --mode production", "dev": "cross-env NODE_ENV=development HTTPS=true webpack serve --mode development"}, "author": "", "type": "module", "license": "ISC", "description": "", "devDependencies": {"@types/loader-utils": "^2.0.6", "@types/node": "^22.8.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-lottie": "^1.2.10", "@typescript-eslint/eslint-plugin": "^8.13.0", "@typescript-eslint/parser": "^8.13.0", "babel-plugin-transform-css-modules": "^0.0.1", "clean-webpack-plugin": "^4.0.0", "concurrently": "^9.0.1", "copy-webpack-plugin": "^12.0.2", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "eslint": "^9.14.0", "eslint-plugin-unused-imports": "^4.1.4", "eslint-webpack-plugin": "^4.2.0", "html-webpack-plugin": "^5.6.3", "mini-css-extract-plugin": "^2.9.1", "sass": "^1.80.4", "sass-loader": "^16.0.2", "style-loader": "^4.0.0", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "typescript": "^5.6.3", "webpack": "^5.95.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0"}, "dependencies": {"@twa-dev/sdk": "^7.10.1", "@types/i18next": "^12.1.0", "@types/react-i18next": "^7.8.3", "date-fns": "^4.1.0", "framer-motion": "^11.11.10", "i18next": "^23.16.4", "loader-utils": "^3.3.1", "lottie-web": "^5.12.2", "postcss-loader": "^8.1.1", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.1.0", "react-router-dom": "^6.27.0", "ts-node-dev": "^2.0.0"}}