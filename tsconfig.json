{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "noEmit": true, "types": ["node", "react", "react-dom"], "baseUrl": ".", "paths": {"@/*": ["./*"]}}, "include": ["./**/*.ts", "./**/*.tsx", "./**/*.js", "./**/*.jsx"], "exclude": ["node_modules", "coverage"]}